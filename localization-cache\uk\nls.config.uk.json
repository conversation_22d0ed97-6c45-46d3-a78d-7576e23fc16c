{"vscode": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\main.i18n.json", "vscode.bat": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\bat.i18n.json", "vscode.clojure": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\clojure.i18n.json", "vscode.coffeescript": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\coffeescript.i18n.json", "vscode.configuration-editing": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\configuration-editing.i18n.json", "vscode.cpp": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\cpp.i18n.json", "vscode.csharp": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\csharp.i18n.json", "vscode.css-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\css-language-features.i18n.json", "vscode.css": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\css.i18n.json", "vscode.debug-auto-launch": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\debug-auto-launch.i18n.json", "vscode.debug-server-ready": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\debug-server-ready.i18n.json", "vscode.docker": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\docker.i18n.json", "vscode.emmet": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\emmet.i18n.json", "vscode.extension-editing": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\extension-editing.i18n.json", "vscode.fsharp": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\fsharp.i18n.json", "vscode.git-ui": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\git-ui.i18n.json", "vscode.git": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\git.i18n.json", "vscode.github-authentication": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\github-authentication.i18n.json", "vscode.go": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\go.i18n.json", "vscode.groovy": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\groovy.i18n.json", "vscode.grunt": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\grunt.i18n.json", "vscode.gulp": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\gulp.i18n.json", "vscode.handlebars": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\handlebars.i18n.json", "vscode.hlsl": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\hlsl.i18n.json", "vscode.html-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\html-language-features.i18n.json", "vscode.html": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\html.i18n.json", "vscode.image-preview": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\image-preview.i18n.json", "vscode.ini": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\ini.i18n.json", "vscode.jake": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\jake.i18n.json", "vscode.java": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\java.i18n.json", "vscode.javascript": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\javascript.i18n.json", "vscode.json-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\json-language-features.i18n.json", "vscode.json": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\json.i18n.json", "vscode.less": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\less.i18n.json", "vscode.log": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\log.i18n.json", "vscode.lua": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\lua.i18n.json", "vscode.make": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\make.i18n.json", "vscode.markdown-basics": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\markdown-basics.i18n.json", "vscode.markdown-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\markdown-language-features.i18n.json", "vscode.merge-conflict": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\merge-conflict.i18n.json", "vscode.ms-vscode.js-debug-nightly": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\ms-vscode.js-debug-nightly.i18n.json", "vscode.ms-vscode.node-debug": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\ms-vscode.node-debug.i18n.json", "vscode.ms-vscode.node-debug2": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\ms-vscode.node-debug2.i18n.json", "vscode.npm": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\npm.i18n.json", "vscode.objective-c": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\objective-c.i18n.json", "vscode.perl": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\perl.i18n.json", "vscode.php-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\php-language-features.i18n.json", "vscode.php": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\php.i18n.json", "vscode.powershell": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\powershell.i18n.json", "vscode.pug": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\pug.i18n.json", "vscode.python": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\python.i18n.json", "vscode.r": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\r.i18n.json", "vscode.razor": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\razor.i18n.json", "vscode.ruby": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\ruby.i18n.json", "vscode.rust": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\rust.i18n.json", "vscode.scss": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\scss.i18n.json", "vscode.search-result": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\search-result.i18n.json", "vscode.shaderlab": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\shaderlab.i18n.json", "vscode.shellscript": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\shellscript.i18n.json", "vscode.sql": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\sql.i18n.json", "vscode.swift": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\swift.i18n.json", "vscode.theme-abyss": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-abyss.i18n.json", "vscode.theme-defaults": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-defaults.i18n.json", "vscode.theme-kimbie-dark": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-kimbie-dark.i18n.json", "vscode.theme-monokai-dimmed": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-monokai-dimmed.i18n.json", "vscode.theme-monokai": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-monokai.i18n.json", "vscode.theme-quietlight": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-quietlight.i18n.json", "vscode.theme-red": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-red.i18n.json", "vscode.theme-seti": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-seti.i18n.json", "vscode.theme-solarized-dark": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-solarized-dark.i18n.json", "vscode.theme-solarized-light": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-solarized-light.i18n.json", "vscode.theme-tomorrow-night-blue": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\theme-tomorrow-night-blue.i18n.json", "vscode.typescript-basics": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\typescript-basics.i18n.json", "vscode.typescript-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\typescript-language-features.i18n.json", "vscode.vb": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\vb.i18n.json", "vscode.vscode-account": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\vscode-account.i18n.json", "vscode.xml": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\xml.i18n.json", "vscode.yaml": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-uk\\extension\\translations\\extensions\\yaml.i18n.json"}