{"vscode": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\main.i18n.json", "ms-vscode.js-debug": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\ms-vscode.js-debug.i18n.json", "vscode.bat": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.bat.i18n.json", "vscode.builtin-notebook-renderers": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.builtin-notebook-renderers.i18n.json", "vscode.clojure": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.clojure.i18n.json", "vscode.coffeescript": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.coffeescript.i18n.json", "vscode.configuration-editing": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.configuration-editing.i18n.json", "vscode.cpp": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.cpp.i18n.json", "vscode.csharp": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.csharp.i18n.json", "vscode.css-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.css-language-features.i18n.json", "vscode.css": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.css.i18n.json", "vscode.dart": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.dart.i18n.json", "vscode.debug-auto-launch": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.debug-auto-launch.i18n.json", "vscode.debug-server-ready": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.debug-server-ready.i18n.json", "vscode.diff": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.diff.i18n.json", "vscode.docker": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.docker.i18n.json", "vscode.emmet": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.emmet.i18n.json", "vscode.extension-editing": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.extension-editing.i18n.json", "vscode.fsharp": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.fsharp.i18n.json", "vscode.git-base": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.git-base.i18n.json", "vscode.git": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.git.i18n.json", "vscode.github-authentication": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.github-authentication.i18n.json", "vscode.github": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.github.i18n.json", "vscode.go": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.go.i18n.json", "vscode.groovy": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.groovy.i18n.json", "vscode.grunt": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.grunt.i18n.json", "vscode.gulp": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.gulp.i18n.json", "vscode.handlebars": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.handlebars.i18n.json", "vscode.hlsl": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.hlsl.i18n.json", "vscode.html-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.html-language-features.i18n.json", "vscode.html": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.html.i18n.json", "vscode.ini": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.ini.i18n.json", "vscode.ipynb": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.ipynb.i18n.json", "vscode.jake": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.jake.i18n.json", "vscode.java": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.java.i18n.json", "vscode.javascript": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.javascript.i18n.json", "vscode.json-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.json-language-features.i18n.json", "vscode.json": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.json.i18n.json", "vscode.julia": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.julia.i18n.json", "vscode.latex": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.latex.i18n.json", "vscode.less": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.less.i18n.json", "vscode.log": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.log.i18n.json", "vscode.lua": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.lua.i18n.json", "vscode.make": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.make.i18n.json", "vscode.markdown-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.markdown-language-features.i18n.json", "vscode.markdown-math": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.markdown-math.i18n.json", "vscode.markdown": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.markdown.i18n.json", "vscode.media-preview": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.media-preview.i18n.json", "vscode.merge-conflict": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.merge-conflict.i18n.json", "vscode.microsoft-authentication": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.microsoft-authentication.i18n.json", "vscode.npm": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.npm.i18n.json", "vscode.objective-c": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.objective-c.i18n.json", "vscode.perl": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.perl.i18n.json", "vscode.php-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.php-language-features.i18n.json", "vscode.php": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.php.i18n.json", "vscode.powershell": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.powershell.i18n.json", "vscode.pug": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.pug.i18n.json", "vscode.python": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.python.i18n.json", "vscode.r": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.r.i18n.json", "vscode.razor": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.razor.i18n.json", "vscode.references-view": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.references-view.i18n.json", "vscode.restructuredtext": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.restructuredtext.i18n.json", "vscode.ruby": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.ruby.i18n.json", "vscode.rust": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.rust.i18n.json", "vscode.scss": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.scss.i18n.json", "vscode.search-result": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.search-result.i18n.json", "vscode.shaderlab": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.shaderlab.i18n.json", "vscode.shellscript": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.shellscript.i18n.json", "vscode.simple-browser": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.simple-browser.i18n.json", "vscode.sql": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.sql.i18n.json", "vscode.swift": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.swift.i18n.json", "vscode.theme-abyss": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-abyss.i18n.json", "vscode.theme-defaults": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-defaults.i18n.json", "vscode.theme-kimbie-dark": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-kimbie-dark.i18n.json", "vscode.theme-monokai-dimmed": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-monokai-dimmed.i18n.json", "vscode.theme-monokai": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-monokai.i18n.json", "vscode.theme-quietlight": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-quietlight.i18n.json", "vscode.theme-red": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-red.i18n.json", "vscode.theme-solarized-dark": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-solarized-dark.i18n.json", "vscode.theme-solarized-light": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-solarized-light.i18n.json", "vscode.theme-tomorrow-night-blue": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.theme-tomorrow-night-blue.i18n.json", "vscode.tunnel-forwarding": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.tunnel-forwarding.i18n.json", "vscode.typescript-language-features": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.typescript-language-features.i18n.json", "vscode.typescript": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.typescript.i18n.json", "vscode.vb": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.vb.i18n.json", "vscode.vscode-theme-seti": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.vscode-theme-seti.i18n.json", "vscode.xml": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.xml.i18n.json", "vscode.yaml": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Arduino IDE\\resources\\app\\plugins\\vscode-language-pack-zh-hant\\extension\\translations\\extensions\\vscode.yaml.i18n.json"}