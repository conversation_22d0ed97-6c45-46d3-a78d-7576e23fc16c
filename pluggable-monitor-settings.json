{"arduino:avr:uno-COM3-serial": {"bits": {"id": "bits", "label": "Data bits", "type": "enum", "values": ["5", "6", "7", "8", "9"], "selectedValue": "8"}, "dtr": {"id": "dtr", "label": "DTR", "type": "enum", "values": ["on", "off"], "selectedValue": "on"}, "parity": {"id": "parity", "label": "Parity", "type": "enum", "values": ["none", "even", "odd", "mark", "space"], "selectedValue": "none"}, "rts": {"id": "rts", "label": "RTS", "type": "enum", "values": ["on", "off"], "selectedValue": "on"}, "stop_bits": {"id": "stop_bits", "label": "Stop bits", "type": "enum", "values": ["1", "1.5", "2"], "selectedValue": "1"}, "baudrate": {"id": "baudrate", "label": "Baudrate", "type": "enum", "values": ["300", "600", "750", "1200", "2400", "4800", "9600", "19200", "31250", "38400", "57600", "74880", "115200", "230400", "250000", "460800", "500000", "921600", "1000000", "2000000"], "selectedValue": "9600"}}, "arduino:avr:uno-COM5-serial": {"bits": {"id": "bits", "label": "Data bits", "type": "enum", "values": ["5", "6", "7", "8", "9"], "selectedValue": "8"}, "dtr": {"id": "dtr", "label": "DTR", "type": "enum", "values": ["on", "off"], "selectedValue": "on"}, "parity": {"id": "parity", "label": "Parity", "type": "enum", "values": ["none", "even", "odd", "mark", "space"], "selectedValue": "none"}, "rts": {"id": "rts", "label": "RTS", "type": "enum", "values": ["on", "off"], "selectedValue": "on"}, "stop_bits": {"id": "stop_bits", "label": "Stop bits", "type": "enum", "values": ["1", "1.5", "2"], "selectedValue": "1"}, "baudrate": {"id": "baudrate", "label": "Baudrate", "type": "enum", "values": ["300", "600", "750", "1200", "2400", "4800", "9600", "19200", "31250", "38400", "57600", "74880", "115200", "230400", "250000", "460800", "500000", "921600", "1000000", "2000000"], "selectedValue": "9600"}}, "arduino:avr:mega-COM4-serial": {"rts": {"id": "rts", "label": "RTS", "type": "enum", "values": ["on", "off"], "selectedValue": "on"}, "stop_bits": {"id": "stop_bits", "label": "Stop bits", "type": "enum", "values": ["1", "1.5", "2"], "selectedValue": "1"}, "baudrate": {"id": "baudrate", "label": "Baudrate", "type": "enum", "values": ["300", "600", "750", "1200", "2400", "4800", "9600", "19200", "31250", "38400", "57600", "74880", "115200", "230400", "250000", "460800", "500000", "921600", "1000000", "2000000"], "selectedValue": "9600"}, "bits": {"id": "bits", "label": "Data bits", "type": "enum", "values": ["5", "6", "7", "8", "9"], "selectedValue": "8"}, "dtr": {"id": "dtr", "label": "DTR", "type": "enum", "values": ["on", "off"], "selectedValue": "on"}, "parity": {"id": "parity", "label": "Parity", "type": "enum", "values": ["none", "even", "odd", "mark", "space"], "selectedValue": "none"}}, "arduino:avr:circuitplay32u4cat-COM4-serial": {"baudrate": {"id": "baudrate", "label": "Baudrate", "type": "enum", "values": ["300", "600", "750", "1200", "2400", "4800", "9600", "19200", "31250", "38400", "57600", "74880", "115200", "230400", "250000", "460800", "500000", "921600", "1000000", "2000000"], "selectedValue": "9600"}, "bits": {"id": "bits", "label": "Data bits", "type": "enum", "values": ["5", "6", "7", "8", "9"], "selectedValue": "8"}, "dtr": {"id": "dtr", "label": "DTR", "type": "enum", "values": ["on", "off"], "selectedValue": "on"}, "parity": {"id": "parity", "label": "Parity", "type": "enum", "values": ["none", "even", "odd", "mark", "space"], "selectedValue": "none"}, "rts": {"id": "rts", "label": "RTS", "type": "enum", "values": ["on", "off"], "selectedValue": "on"}, "stop_bits": {"id": "stop_bits", "label": "Stop bits", "type": "enum", "values": ["1", "1.5", "2"], "selectedValue": "1"}}}