#include <Wire.h>
#include <LiquidCrystal_I2C.h>
#include <string.h>
#include <stdlib.h>

LiquidCrystal_I2C lcd(0x27, 16, 2);

#define DIGITS 200   // how many digits of Pi to compute
#define JOY_X A0     // analog pin connected to joystick X-axis

char piDigits[DIGITS + 2];
int currentIndex = 0;

void computePi() {
  int len = DIGITS * 10 / 3 + 1;
  // Use dynamic allocation to avoid stack overflow on Arduino
  int* a = (int*)malloc(len * sizeof(int));
  if (a == NULL) {
    // Handle memory allocation failure
    strcpy(piDigits, "3.14159"); // fallback to basic Pi
    return;
  }
  for (int i = 0; i < len; i++) a[i] = 2;

  int nines = 0;
  int predigit = 0;

  piDigits[0] = '3';
  piDigits[1] = '.';
  int pos = 2;

  for (int j = 0; j < DIGITS; j++) {
    int q = 0;
    for (int i = len - 1; i > 0; i--) {
      int x = 10 * a[i] + q * i;
      a[i] = x % (2 * i + 1);
      q = x / (2 * i + 1);
    }
    a[0] = q % 10;
    q = q / 10;

    if (q == 9) {
      nines++;
    } else if (q == 10) {
      piDigits[pos++] = predigit + '1';
      for (int k = 0; k < nines; k++) piDigits[pos++] = '0';
      predigit = 0;
      nines = 0;
    } else {
      piDigits[pos++] = predigit + '0';
      for (int k = 0; k < nines; k++) piDigits[pos++] = '9';
      predigit = q;
      nines = 0;
    }
  }
  piDigits[pos++] = predigit + '0';
  piDigits[pos] = '\0';

  // Free the dynamically allocated memory
  free(a);
}

void setup() {
  lcd.init();
  lcd.backlight();
  computePi();
  lcd.clear();
  lcd.setCursor(0, 0);
  lcd.print(piDigits[currentIndex]);
}

void loop() {
  int joyX = analogRead(JOY_X);

  // Thresholds to detect left/right movement
  if (joyX < 400) {        // left
    currentIndex--;
    if (currentIndex < 0) currentIndex = 0;
    updateLCD();
    delay(150);
  } 
  else if (joyX > 600) {   // right
    currentIndex++;
    if (currentIndex >= strlen(piDigits)) currentIndex = strlen(piDigits) - 1;
    updateLCD();
    delay(150);
  }
}

void updateLCD() {
  lcd.clear();

  // Display up to 16 characters on first line, 16 on second line
  int piLen = strlen(piDigits);

  // First line
  lcd.setCursor(0, 0);
  for (int i = 0; i < 16 && (currentIndex + i) < piLen; i++) {
    lcd.print(piDigits[currentIndex + i]);
  }

  // Second line (if there are more digits)
  if (currentIndex + 16 < piLen) {
    lcd.setCursor(0, 1);
    for (int i = 16; i < 32 && (currentIndex + i) < piLen; i++) {
      lcd.print(piDigits[currentIndex + i]);
    }
  }
}
